# 服务套餐管理模块接口文档生成提示词

## 1. 模块概述

请根据以下数据库表结构和前端页面功能，为**服务套餐管理模块**生成完整的RESTful API接口文档。

### 数据库表结构：

- **主表**：`publicbiz_service_package` - 服务套餐主表
- **关联表1**：`publicbiz_package_carousel` - 服务套餐轮播图表
- **关联表2**：`publicbiz_package_feature` - 服务套餐特色标签表

### 前端页面功能：

- 套餐列表展示（支持分页、筛选、状态切换）
- 套餐新增/编辑（抽屉形式）
- 套餐状态管理（上架/下架/删除）
- 图片上传功能
- 特色标签管理

---

## 2. 接口需求详细说明

### 2.1 套餐列表查询接口

**功能**：分页查询服务套餐列表，支持多条件筛选 **前端调用场景**：

- 页面初始化加载套餐列表
- 切换标签页（已上架/待上架/回收站）时重新加载
- 搜索筛选时重新加载

**查询参数要求**：

- `keyword`：套餐名称或ID模糊搜索
- `category`：服务分类筛选
- `status`：状态筛选（active-已上架/pending-待上架/deleted-回收站）
- `packageType`：套餐类型筛选
- `pageNum`：当前页码
- `pageSize`：每页数量

**返回数据要求**：

- 包含套餐基本信息（id、name、category、price、unit、packageType、status等）
- 包含特色标签数组
- 包含轮播图数组
- 包含分页信息（total、pageNum、pageSize）

### 2.2 套餐详情查询接口

**功能**：根据ID查询套餐详细信息 **前端调用场景**：编辑套餐时加载详情数据

**返回数据要求**：

- 包含套餐所有字段信息
- 包含关联的轮播图列表
- 包含关联的特色标签列表
- 包含完整的预约配置信息

### 2.3 套餐新增接口

**功能**：创建新的服务套餐 **前端调用场景**：点击"添加新商品"按钮提交表单

**请求参数要求**：

- 基础信息：name、category、thumbnail、price、originalPrice、unit、serviceDuration
- 套餐类型：packageType、taskSplitRule
- 特色标签：features（数组）
- 服务详情：serviceDescription、serviceDetails、serviceProcess、purchaseNotice
- 预约配置：advanceBookingDays、timeSelectionMode、appointmentMode、serviceStartTime、addressSetting、maxBookingDays、cancellationPolicy
- 状态：status

**特殊处理要求**：

- 特色标签需要同时插入到关联表
- 轮播图需要同时插入到关联表
- 支持图片上传功能

### 2.4 套餐更新接口

**功能**：更新现有套餐信息 **前端调用场景**：编辑套餐后提交表单

**请求参数要求**：

- 与新增接口相同的参数结构
- 支持部分字段更新
- 特色标签和轮播图的增删改操作

### 2.5 套餐状态更新接口

**功能**：批量更新套餐状态（上架/下架/删除） **前端调用场景**：点击操作列的"上架"/"下架"按钮

**请求参数要求**：

- `ids`：套餐ID数组
- `status`：目标状态

### 2.6 套餐删除接口

**功能**：删除套餐（软删除） **前端调用场景**：点击操作列的"删除"按钮

### 2.7 图片上传接口

**功能**：上传套餐主图和轮播图 **前端调用场景**：表单中的图片上传组件

**要求**：

- 支持主图和轮播图上传
- 返回图片URL
- 文件大小限制（2MB）
- 文件类型限制（图片格式）

### 2.8 特色标签管理接口

**功能**：管理套餐的特色标签 **前端调用场景**：表单中的特色标签添加/删除

**要求**：

- 支持标签的增删改查
- 支持标签排序
- 支持推荐标签列表

---

## 3. 数据模型要求

### 3.1 套餐主表字段映射

请确保以下字段正确映射：

- `id` → 套餐ID
- `name` → 套餐名称
- `category` → 服务分类
- `thumbnail` → 套餐主图URL
- `price` → 套餐价格
- `original_price` → 原价
- `unit` → 价格单位
- `service_duration` → 服务时长
- `package_type` → 套餐类型
- `task_split_rule` → 任务拆分规则
- `service_description` → 服务描述
- `service_details` → 详细服务内容
- `service_process` → 服务流程
- `purchase_notice` → 购买须知
- `status` → 状态
- 预约配置相关字段

### 3.2 关联表处理

- 轮播图数据存储在 `publicbiz_package_carousel` 表
- 特色标签数据存储在 `publicbiz_package_feature` 表
- 需要处理一对多关系

---

## 4. 接口规范要求

### 4.1 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01 12:00:00"
}
```

### 4.2 分页响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "total": 100,
    "pageNum": 1,
    "pageSize": 20
  }
}
```

### 4.3 错误处理

- 参数验证错误：400
- 权限不足：403
- 资源不存在：404
- 服务器错误：500

### 4.4 接口路径规范

- 列表查询：`GET /publicbiz/employment/service-package/list`
- 详情查询：`GET /publicbiz/employment/service-package/{id}`
- 新增套餐：`POST /publicbiz/employment/service-package`
- 更新套餐：`PUT /publicbiz/employment/service-package/{id}`
- 删除套餐：`DELETE /publicbiz/employment/service-package/{id}`
- 状态更新：`PUT /publicbiz/employment/service-package/status`
- 图片上传(使用现有文件上传接口)：`POST /infra/file/upload`

---

## 5. 特殊要求

### 5.1 多租户支持

- 所有接口需要支持 `tenant_id` 字段
- 数据隔离，只能操作当前租户的数据

### 5.2 软删除支持

- 删除操作使用软删除（设置 `deleted = 1`）
- 查询时需要过滤已删除的数据

### 5.3 操作日志

- 记录创建人、更新人、创建时间、更新时间
- 支持操作审计
- 要介入操作日志模块

### 5.4 数据验证

- 套餐名称不能重复
- 价格必须大于0
- 图片URL格式验证
- 必填字段验证

### 5.5 性能优化

- 列表查询支持索引优化
- 关联查询使用JOIN或分步查询
- 图片上传支持CDN

---

## 6. 生成要求

请根据以上需求生成：

1. **完整的接口文档**（包含所有接口的详细说明）
2. **请求/响应示例**（JSON格式）
3. **错误码说明**
4. **数据库操作SQL示例**
5. **接口测试用例**

文档格式要求：

- 使用Markdown格式
- 包含接口路径、请求方法、参数说明、响应示例
- 代码示例要可直接使用
- 注释要详细清晰

请确保生成的接口文档完全对应前端页面功能和数据库表结构，便于后端开发人员直接实现。

---

## 7. 前端页面功能分析

### 7.1 列表页面功能

- **标签页切换**：已上架(12)、待上架(3)、回收站(2)
- **搜索筛选**：套餐名称/ID、服务分类
- **表格展示**：ID/序号、套餐主图、套餐名称、分类、价格、单位、套餐类型、任务拆分规则、状态、创建时间
- **操作按钮**：编辑、上架/下架、删除
- **分页功能**：支持分页显示

### 7.2 新增/编辑页面功能

- **基础信息**：套餐名称、服务分类、套餐主图、套餐轮播图
- **价格与规格**：套餐价格、价格单位、服务时长
- **套餐类型与特色标签**：套餐类型选择、特色标签管理
- **服务详情**：服务描述、详细服务内容（富文本）
- **购买须知**：重要提醒和注意事项
- **预约配置**：预约时间范围、时间选择模式、预约模式、服务开始时间、地址设置、商品状态

### 7.3 数据交互需求

- **图片上传**：支持主图和轮播图上传
- **特色标签**：支持动态添加/删除标签
- **富文本编辑**：支持服务详情的富文本编辑
- **表单验证**：必填字段验证、格式验证
- **状态管理**：支持套餐状态的上架/下架/删除

---

## 8. 数据库表字段详细说明

### 8.1 主表字段 (publicbiz_service_package)

```sql
-- 基础字段
id: 主键，自增
name: 套餐名称，VARCHAR(200)，必填
category: 服务分类，VARCHAR(50)，必填
thumbnail: 套餐主图URL，VARCHAR(500)
price: 套餐价格，DECIMAL(10,2)，必填
original_price: 原价，DECIMAL(10,2)
unit: 价格单位，VARCHAR(20)，必填
service_duration: 服务时长，VARCHAR(100)
package_type: 套餐类型，VARCHAR(20)，必填
task_split_rule: 任务拆分规则，VARCHAR(200)

-- 服务详情字段
service_description: 服务描述，TEXT
service_details: 详细服务内容，LONGTEXT
service_process: 服务流程，LONGTEXT
purchase_notice: 购买须知，TEXT

-- 状态字段
status: 状态，VARCHAR(20)，默认'pending'

-- 预约配置字段
advance_booking_days: 预约时间范围，INT，默认1
time_selection_mode: 时间选择模式，VARCHAR(20)，默认'fixed'
appointment_mode: 预约模式，VARCHAR(20)，默认'start-date'
service_start_time: 服务开始时间，VARCHAR(20)，默认'within-3-days'
address_setting: 地址设置，VARCHAR(20)，默认'fixed'
max_booking_days: 最大预约天数，INT，默认30
cancellation_policy: 取消政策，VARCHAR(500)
```

### 8.2 轮播图表字段 (publicbiz_package_carousel)

```sql
package_id: 套餐ID，BIGINT，外键
image_url: 轮播图URL，VARCHAR(500)，必填
sort_order: 排序，INT，默认0
status: 状态，TINYINT(1)，默认1
```

### 8.3 特色标签表字段 (publicbiz_package_feature)

```sql
package_id: 套餐ID，BIGINT，外键
feature_name: 特色标签名称，VARCHAR(100)，必填
sort_order: 排序，INT，默认0
```

---

## 9. 接口实现建议

### 9.1 查询接口实现

- 使用LEFT JOIN关联查询轮播图和特色标签
- 支持动态SQL条件拼接
- 使用分页插件实现分页
- 缓存常用查询结果

### 9.2 新增/更新接口实现

- 使用事务确保数据一致性
- 先插入主表，再插入关联表
- 支持批量操作特色标签
- 图片上传使用异步处理

### 9.3 状态管理接口实现

- 支持批量状态更新
- 记录状态变更日志
- 支持状态变更通知

### 9.4 图片上传接口实现

- 支持多种图片格式
- 自动生成缩略图
- 支持图片压缩
- 返回CDN链接

---

## 10. 测试用例建议

### 10.1 功能测试

- 套餐CRUD操作测试
- 图片上传功能测试
- 特色标签管理测试
- 状态变更测试

### 10.2 性能测试

- 大量数据查询性能
- 图片上传并发测试
- 数据库连接池测试

### 10.3 安全测试

- 参数验证测试
- SQL注入防护测试
- 权限控制测试
- 文件上传安全测试

---

请根据以上详细说明生成完整的接口文档，确保文档的准确性和实用性。
