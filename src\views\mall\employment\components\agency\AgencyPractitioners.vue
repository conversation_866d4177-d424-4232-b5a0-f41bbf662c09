<!--
  页面名称：机构旗下阿姨
  功能描述：展示机构的阿姨列表，支持筛选、查看详情等操作
-->
<template>
  <div class="agency-practitioners">
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-input
        v-model="searchForm.keyword"
        placeholder="输入阿姨姓名/手机号"
        style="width: 200px"
        clearable
      />
      <el-select
        v-model="searchForm.serviceType"
        placeholder="所有服务类型"
        clearable
        style="width: 150px"
      >
        <el-option label="月嫂" value="maternity" />
        <el-option label="育儿嫂" value="childcare" />
        <el-option label="保洁" value="cleaning" />
      </el-select>
      <el-select
        v-model="searchForm.platformStatus"
        placeholder="所有平台状态"
        clearable
        style="width: 150px"
      >
        <el-option label="合作中" value="cooperating" />
        <el-option label="已解约" value="terminated" />
      </el-select>
      <el-select v-model="searchForm.rating" placeholder="所有评级" clearable style="width: 150px">
        <el-option label="五星" value="5" />
        <el-option label="四星" value="4" />
        <el-option label="三星及以下" value="3" />
      </el-select>
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="onReset">重置</el-button>
      <el-button type="success" @click="onExport">导出列表</el-button>
      <el-button type="primary" @click="onAdd">新增阿姨</el-button>
    </div>

    <!-- 阿姨列表 -->
    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column prop="id" label="阿姨ID/姓名" min-width="150">
        <template #default="scope">
          <div>
            <strong>{{ scope.row.name }}</strong>
            <br />
            <small>{{ scope.row.id }}</small>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="rating" label="综合评级" width="120">
        <template #default="scope">
          <div>
            {{ scope.row.rating }}
            <i class="fas fa-star" style="color: #f39c12"></i>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="serviceType" label="服务类型" width="100" />
      <el-table-column prop="totalOrders" label="累计单数" width="100" />
      <el-table-column prop="currentStatus" label="当前状态" width="120">
        <template #default="scope">
          <div>
            {{ scope.row.currentStatus }}
            <br />
            <a v-if="scope.row.currentOrder" href="#" style="font-size: 12px">
              {{ scope.row.currentOrder }}
            </a>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="platformStatus" label="平台状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusTag(scope.row.platformStatus)" size="small">
            {{ scope.row.platformStatusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="onView(scope.row)">查看</el-button>
          <el-dropdown>
            <el-button size="small">
              <i class="fas fa-ellipsis-h"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="onEdit(scope.row)">编辑</el-dropdown-item>
                <el-dropdown-item @click="onGenerateResume(scope.row)"
                  >生成简历海报</el-dropdown-item
                >
                <el-dropdown-item @click="onTerminate(scope.row)" divided>解约</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getPractitionerList } from '@/api/mall/employment/practitioner'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

/** 搜索表单数据 */
const searchForm = reactive({
  keyword: '',
  serviceType: '',
  platformStatus: '',
  rating: ''
})

/** 表格数据 */
const tableData = ref([])

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 获取阿姨列表 */
const fetchList = async () => {
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      agencyId: props.agency?.id,
      ...searchForm
    }
    const res = await getPractitionerList(params)
    tableData.value = res.data.list || []
    pagination.total = res.data.total || 0
  } catch (error) {
    console.error('获取阿姨列表失败:', error)
  }
}

/** 搜索 */
const onSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 重置 */
const onReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    serviceType: '',
    platformStatus: '',
    rating: ''
  })
  pagination.page = 1
  fetchList()
}

/** 导出列表 */
const onExport = () => {
  console.log('导出阿姨列表')
}

/** 新增阿姨 */
const onAdd = () => {
  console.log('新增阿姨')
}

/** 查看阿姨详情 */
const onView = (row: any) => {
  console.log('查看阿姨详情:', row)
}

/** 编辑阿姨 */
const onEdit = (row: any) => {
  console.log('编辑阿姨:', row)
}

/** 生成简历海报 */
const onGenerateResume = (row: any) => {
  console.log('生成简历海报:', row)
}

/** 解约 */
const onTerminate = (row: any) => {
  console.log('解约阿姨:', row)
}

/** 获取状态标签 */
const getStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    cooperating: 'success',
    terminated: 'danger',
    pending: 'warning'
  }
  return statusMap[status] || 'info'
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

onMounted(() => {
  if (props.agency) {
    fetchList()
  }
})
</script>

<style scoped lang="scss">
.agency-practitioners {
  .filter-bar {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }
}
</style>
