import request from '@/config/axios'

/**
 * 服务套餐管理API接口
 */

// 轮播图信息接口
export interface ServicePackageCarousel {
  id?: number
  packageId?: number
  imageUrl: string
  sortOrder?: number
  status?: number
  createTime?: string
  updateTime?: string
}

// 特色标签信息接口
export interface ServicePackageFeature {
  id?: number
  packageId?: number
  featureName: string
  sortOrder?: number
  createTime?: string
  updateTime?: string
}

// 服务套餐信息接口
export interface ServicePackage {
  id: number
  name: string
  category: string
  thumbnail: string
  price: number
  originalPrice: number
  unit: string
  serviceDuration: string
  packageType: string
  taskSplitRule: string
  serviceDescription: string
  serviceDetails: string
  serviceProcess: string
  purchaseNotice: string
  status: string
  advanceBookingDays: number
  timeSelectionMode: string
  appointmentMode: string
  serviceStartTime: string
  addressSetting: string
  maxBookingDays: number
  cancellationPolicy: string
  carouselList: ServicePackageCarousel[]
  featureList: ServicePackageFeature[]
  createTime: string
  updateTime: string
}

// 查询参数接口
export interface ServicePackageQueryParams {
  pageNo: number
  pageSize: number
  keyword?: string
  category?: string
  status?: string
  packageType?: string
}

// 新增套餐参数接口
export interface CreateServicePackageParams {
  name: string
  category: string
  thumbnail?: string
  price: number
  originalPrice?: number
  unit: string
  serviceDuration?: string
  packageType: string
  taskSplitRule?: string
  serviceDescription?: string
  serviceDetails?: string
  serviceProcess?: string
  purchaseNotice?: string
  status?: string
  advanceBookingDays?: number
  timeSelectionMode?: string
  appointmentMode?: string
  serviceStartTime?: string
  addressSetting?: string
  maxBookingDays?: number
  cancellationPolicy?: string
  carouselList?: ServicePackageCarousel[]
  featureList?: ServicePackageFeature[]
}

// 更新套餐参数接口
export interface UpdateServicePackageParams {
  id: number
  name: string
  category: string
  thumbnail?: string
  price: number
  originalPrice?: number
  unit: string
  serviceDuration?: string
  packageType: string
  taskSplitRule?: string
  serviceDescription?: string
  serviceDetails?: string
  serviceProcess?: string
  purchaseNotice?: string
  status?: string
  advanceBookingDays?: number
  timeSelectionMode?: string
  appointmentMode?: string
  serviceStartTime?: string
  addressSetting?: string
  maxBookingDays?: number
  cancellationPolicy?: string
  carouselList?: ServicePackageCarousel[]
  featureList?: ServicePackageFeature[]
}

// 状态更新参数接口
export interface UpdateStatusParams {
  ids: number[]
  status: string
}

// 通用响应接口
export interface ApiResponse<T> {
  code: number
  data: T
  msg: string
}

// 分页响应接口
export interface ServicePackagePageResult {
  list: ServicePackage[]
  total: number
}

/**
 * 获取服务套餐分页列表
 * @param params 查询参数
 * @returns Promise<ApiResponse<ServicePackagePageResult>>
 */
export function getServicePackageList(params: ServicePackageQueryParams) {
  return request.get<ApiResponse<ServicePackagePageResult>>({
    url: '/publicbiz/employment/service-package/page',
    params
  })
}

/**
 * 获取服务套餐详情
 * @param id 套餐ID
 * @returns Promise<ApiResponse<ServicePackage>>
 */
export function getServicePackageDetail(id: number) {
  return request.get<ApiResponse<ServicePackage>>({
    url: `/publicbiz/employment/service-package/${id}`
  })
}

/**
 * 新增服务套餐
 * @param data 套餐信息
 * @returns Promise<ApiResponse<{ id: number }>>
 */
export function createServicePackage(data: CreateServicePackageParams) {
  return request.post<ApiResponse<{ id: number }>>({
    url: '/publicbiz/employment/service-package/create',
    data
  })
}

/**
 * 更新服务套餐
 * @param data 更新信息
 * @returns Promise<ApiResponse<boolean>>
 */
export function updateServicePackage(data: UpdateServicePackageParams) {
  return request.put<ApiResponse<boolean>>({
    url: '/publicbiz/employment/service-package/update',
    data
  })
}

/**
 * 删除服务套餐
 * @param id 套餐ID
 * @returns Promise<ApiResponse<boolean>>
 */
export function deleteServicePackage(id: number) {
  return request.delete<ApiResponse<boolean>>({
    url: `/publicbiz/employment/service-package/${id}`
  })
}

/**
 * 移动服务套餐到回收站
 * @param id 套餐ID
 * @returns Promise<ApiResponse<boolean>>
 */
export function moveServicePackageToRecycle(id: number) {
  return request.delete<ApiResponse<boolean>>({
    url: `/publicbiz/employment/service-package/${id}/move-to-recycle`
  })
}

/**
 * 批量更新服务套餐状态
 * @param data 状态更新参数
 * @returns Promise<ApiResponse<boolean>>
 */
export function updateServicePackageStatus(data: UpdateStatusParams) {
  return request.put<ApiResponse<boolean>>({
    url: '/publicbiz/employment/service-package/status',
    data
  })
}

/**
 * 更新单个服务套餐状态
 * @param id 套餐ID
 * @param status 状态
 * @returns Promise<ApiResponse<boolean>>
 */
export function updateSingleServicePackageStatus(id: number, status: string) {
  return updateServicePackageStatus({ ids: [id], status })
}

/**
 * 获取套餐类型列表
 * @returns Promise<ApiResponse<string[]>>
 */
export function getPackageTypes() {
  return request.get<ApiResponse<string[]>>({
    url: '/publicbiz/employment/service-package/types'
  })
}

/**
 * 导出服务套餐列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportServicePackageList(
  params: Omit<ServicePackageQueryParams, 'pageNo' | 'pageSize'>
) {
  return request.download({
    url: '/publicbiz/employment/service-package/export',
    params
  })
}
