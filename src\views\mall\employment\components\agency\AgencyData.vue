<!--
  页面名称：机构业务数据
  功能描述：展示机构的业务指标、图表等数据
-->
<template>
  <div class="agency-data">
    <!-- 数据筛选 -->
    <div class="data-filter">
      <el-button-group>
        <el-button
          v-for="period in dataPeriods"
          :key="period.value"
          :type="currentPeriod === period.value ? 'primary' : ''"
          @click="changePeriod(period.value)"
        >
          {{ period.label }}
        </el-button>
      </el-button-group>
      <span class="update-time">数据更新于: {{ formatDateTime(updateTime) }}</span>
    </div>

    <!-- 核心业务指标 -->
    <div class="kpi-section">
      <div class="section-title">核心业务指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-file-signature"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.serviceOrders || 0 }}</div>
            <div class="kpi-label">服务订单数</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-handshake"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.interviewSuccessRate || 0 }}%</div>
            <div class="kpi-label">面试成功率</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-star"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.customerSatisfaction || 0 }}%</div>
            <div class="kpi-label">客户好评率</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.complaintRate || 0 }}%</div>
            <div class="kpi-label">客户投诉率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 阿姨相关指标 -->
    <div class="kpi-section">
      <div class="section-title">阿姨相关指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-check"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.totalPractitioners || 0 }}</div>
            <div class="kpi-label">在职阿姨总数</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-plus"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.newPractitioners || 0 }}</div>
            <div class="kpi-label">期间新增阿姨</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-user-slash"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.lostPractitioners || 0 }}</div>
            <div class="kpi-label">期间流失阿姨</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ businessData.activePractitioners || 0 }}</div>
            <div class="kpi-label">期间上单阿姨数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 财务指标 -->
    <div class="kpi-section">
      <div class="section-title">财务指标</div>
      <div class="kpi-grid">
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.totalOrders) }}</div>
            <div class="kpi-label">期间订单总额</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-wallet"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.ourIncome) }}</div>
            <div class="kpi-label">期间我方收入</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-money-check-alt"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.settledAmount) }}</div>
            <div class="kpi-label">期间已结算</div>
          </div>
        </div>
        <div class="kpi-item">
          <div class="kpi-icon">
            <i class="fas fa-hourglass-half"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">{{ formatMoney(businessData.pendingAmount) }}</div>
            <div class="kpi-label">期间待结算</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div class="chart-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单趋势 (近6个月)</h3>
          </div>
          <div class="chart-body">
            <div ref="ordersChart" class="chart-container"></div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>服务类型分布 (近30天)</h3>
          </div>
          <div class="chart-body">
            <div ref="categoryChart" class="chart-container"></div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>服务质量趋势 (近6个月)</h3>
          </div>
          <div class="chart-body">
            <div ref="qualityChart" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

/** 机构数据 */
const props = defineProps<{
  agency: any
}>()

/** 数据周期选项 */
const dataPeriods = [
  { label: '近30天', value: '30' },
  { label: '近90天', value: '90' },
  { label: '本年度', value: 'year' },
  { label: '全部', value: 'all' }
]

/** 当前选中的周期 */
const currentPeriod = ref('30')

/** 更新时间 */
const updateTime = ref(new Date())

/** 业务数据 */
const businessData = reactive({
  serviceOrders: 88,
  interviewSuccessRate: 75.6,
  customerSatisfaction: 98.5,
  complaintRate: 1.2,
  totalPractitioners: 125,
  newPractitioners: 12,
  lostPractitioners: 3,
  activePractitioners: 89,
  totalOrders: 450800,
  ourIncome: 45080,
  settledAmount: 35800,
  pendingAmount: 9280
})

/** 图表引用 */
const ordersChart = ref()
const categoryChart = ref()
const qualityChart = ref()

/** 图表实例 */
let ordersChartInstance: echarts.ECharts | null = null
let categoryChartInstance: echarts.ECharts | null = null
let qualityChartInstance: echarts.ECharts | null = null

/** 切换数据周期 */
const changePeriod = (period: string) => {
  currentPeriod.value = period
  fetchBusinessData()
}

/** 获取业务数据 */
const fetchBusinessData = async () => {
  try {
    // 这里应该调用API获取数据
    console.log('获取业务数据，周期:', currentPeriod.value)
    // 模拟数据更新
    updateTime.value = new Date()
  } catch (error) {
    console.error('获取业务数据失败:', error)
  }
}

/** 初始化订单趋势图表 */
const initOrdersChart = () => {
  if (!ordersChart.value) return

  ordersChartInstance = echarts.init(ordersChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: [15, 22, 18, 25, 30, 28],
        type: 'line',
        smooth: true
      }
    ]
  }
  ordersChartInstance.setOption(option)
}

/** 初始化服务类型分布图表 */
const initCategoryChart = () => {
  if (!categoryChart.value) return

  categoryChartInstance = echarts.init(categoryChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: '月嫂' },
          { value: 25, name: '育儿嫂' },
          { value: 20, name: '保洁' },
          { value: 15, name: '护工' },
          { value: 5, name: '其他' }
        ]
      }
    ]
  }
  categoryChartInstance.setOption(option)
}

/** 初始化服务质量趋势图表 */
const initQualityChart = () => {
  if (!qualityChart.value) return

  qualityChartInstance = echarts.init(qualityChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      min: 90,
      max: 100
    },
    series: [
      {
        data: [95, 96, 97, 98, 98.5, 98.5],
        type: 'line',
        smooth: true
      }
    ]
  }
  qualityChartInstance.setOption(option)
}

/** 格式化金额 */
const formatMoney = (amount: number) => {
  if (!amount) return '¥0.00'
  return `¥${amount.toLocaleString()}`
}

/** 格式化日期时间 */
const formatDateTime = (date: Date) => {
  return date.toLocaleString()
}

/** 监听窗口大小变化 */
const handleResize = () => {
  ordersChartInstance?.resize()
  categoryChartInstance?.resize()
  qualityChartInstance?.resize()
}

onMounted(() => {
  fetchBusinessData()

  // 延迟初始化图表，确保DOM已渲染
  setTimeout(() => {
    initOrdersChart()
    initCategoryChart()
    initQualityChart()
  }, 100)

  window.addEventListener('resize', handleResize)
})

// 监听机构变化
watch(
  () => props.agency,
  () => {
    if (props.agency) {
      fetchBusinessData()
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.agency-data {
  .data-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
  }

  .update-time {
    font-size: 12px;
    color: #6c757d;
  }

  .kpi-section {
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #343a40;
    padding-bottom: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
  }

  .kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .kpi-item {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
  }

  .kpi-icon {
    font-size: 24px;
    color: #3498db;
    margin-right: 15px;
    flex-shrink: 0;
  }

  .kpi-info {
    .kpi-value {
      font-size: 22px;
      font-weight: 700;
      color: #343a40;
      display: block;
    }

    .kpi-label {
      font-size: 13px;
      color: #6c757d;
    }
  }

  .chart-section {
    margin-top: 30px;
  }

  .chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }

  .chart-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
  }

  .chart-header {
    padding: 12px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }
  }

  .chart-body {
    padding: 15px;
  }

  .chart-container {
    width: 100%;
    height: 200px;
  }
}
</style>
