````提示词
请根据数字资产(课程)相关页面的内容，设计并创建对应的数据库表结构。

**任务要求：**
1. 分析以下前端页面文件，提取所有业务字段：
   - 数字资产(课程)列表页：`src\views\infra\ResourceCenter\DigitalAsset\DigitalAsset.vue`
   - 数字资产(课程)新增页面：`src\views\infra\ResourceCenter\DigitalAsset\components\AddDigitalAsset.vue`
   - 数字资产(课程)线下课程编辑页面：`src\views\infra\ResourceCenter\DigitalAsset\components\ManagementCourse.vue`
   - 数字资产(课程)线上课程编辑页面：`src\views\infra\ResourceCenter\DigitalAsset\components\ManagementCourseForOnline.vue`

2. **数据库表设计规范：**
   - 表名前缀：必须使用 `publicbiz_`
   - 每个表必须包含以下标准字段：
     ```sql
     `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
     `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号'
     ```

3. **输出要求：**
   - 仔细分析页面中的所有表单字段、列表字段、筛选条件等，确保不遗漏任何业务字段
   - 根据字段类型和用途，合理设计数据库字段类型、长度、默认值和注释
   - 考虑线上课程和线下课程的差异，设计合适的表结构（可能需要多个表或统一表结构）
   - 生成完整的可执行MySQL CREATE TABLE语句
   - 将最终的SQL语句保存到文件：`提示词\资源中心提示词\资源中心-数字资产(课程)数据库表结构.md`

**注意事项：**
- 确保表结构能够支持页面中的所有业务功能
- 字段命名要清晰、规范，使用下划线命名法
- 为每个字段添加准确的中文注释
- 考虑字段的必填性、唯一性等约束条件
````

# 资源中心-数字资产(课程)数据库表结构

## 业务字段分析

### 从页面分析提取的字段：

#### 列表页筛选字段：

- 课程分类 (category)
- 课程状态 (status)
- 授课方式 (teachType)
- 业务板块 (businessModule)
- 收款商户 (merchant)
- 课程名称关键词搜索 (keyword)

#### 列表页展示字段：

- 课程封面 (cover)
- 课程名称 (name)
- 课程分类 (category)
- 授课方式 (teachType)
- 关联讲师 (teacher_id)
- 所属业务板块 (businessModule)
- 收款商户 (merchant)
- 状态 (status)

#### 新增/编辑页面字段：

**通用字段：**

- 课程名称 (name) - 必填
- 授课方式 (teachType) - 必填，线上授课/线下授课
- 课程封面 (cover) - 文件上传
- 课程分类 (category) - 必填，家政技能/职业素养/高校实践/企业管理
- 课程状态 (status) - 必填，待发布/已上架/已下架
- 关联讲师 (teacher_id)
- 所属业务板块 (businessModule) - 家政服务/高校实践/培训管理/就业服务/兼职零工
- 收款商户 (merchant) - 汇成人力资源/汇成家政服务/汇成培训中心/汇成就业服务
- 课程描述 (desc) - 文本域

**线下授课专用字段：**

- 上课地址/地点 (location)
- 排期 (schedule)
- 总名额 (totalSeats/total)
- 已报名人数 (enrolledCount/enrolled)

**线上授课专用字段：**

- 课程总时长(小时) (totalDuration)
- 已报名人数 (enrolled)

#### 线上课程大纲相关字段：

- 章节标题 (chapter.title)
- 课时标题 (lesson.title)
- 课时类型 (lesson.type) - 视频/文档/音频
- 免费试看 (lesson.isFree) - 布尔值
- 关联素材 (lesson.material)

#### 课程附件相关字段：

- 附件名称 (attachment.name)
- 附件类型 (attachment.type) - 视频/文档

## 数据库表结构设计

### 1. 主课程表 (publicbiz_digital_asset_course)

```sql
CREATE TABLE `publicbiz_digital_asset_course` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '课程名称',
  `teach_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授课方式：线上授课、线下授课',
  `cover_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '课程封面图片URL',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '课程分类：家政技能、职业素养、高校实践、企业管理',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '待发布' COMMENT '课程状态：待发布、已上架、已下架',
  `teacher_id` bigint(20) DEFAULT NULL COMMENT '关联讲师ID',
  `teacher_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联讲师名称',
  `business_module` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '所属业务板块：家政服务、高校实践、培训管理、就业服务、兼职零工',
  `merchant` bigint(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '收款商户ID',
  `merchant_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '收款商户名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '课程详情介绍',

  -- 线下授课专用字段
  `location` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '上课地点（线下授课专用）',
  `schedule` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '排期安排（线下授课专用）',
  `total_seats` int(11) DEFAULT 0 COMMENT '总名额（线下授课专用）',
  `enrolled_count` int(11) DEFAULT 0 COMMENT '已报名人数',

  -- 线上授课专用字段
  `total_duration` decimal(10,2) DEFAULT 0.00 COMMENT '课程总时长（小时，线上授课专用）',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_teach_type` (`teach_type`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_teacher_id` (`teacher_id`),
  KEY `idx_business_module` (`business_module`),
  KEY `idx_merchant` (`merchant`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数字资产课程表';
```

### 2. 课程章节表 (publicbiz_course_chapter)

```sql
CREATE TABLE `publicbiz_course_chapter` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '章节ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '章节标题',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序序号',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程章节表（线上课程专用）';
```

### 3. 课程课时表 (publicbiz_course_lesson)

```sql
CREATE TABLE `publicbiz_course_lesson` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '课时ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `chapter_id` bigint(20) NOT NULL COMMENT '章节ID',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '课时标题',
  `lesson_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '视频' COMMENT '课时类型：视频、文档、音频',
  `is_free` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否免费试看',
  `material_id` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联素材ID',
  `material_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联素材名称',
  `material_file_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联素材文件URL',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序序号',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_lesson_type` (`lesson_type`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程课时表（线上课程专用）';
```

### 4. 课程附件表 (publicbiz_course_attachment)

```sql
CREATE TABLE `publicbiz_course_attachment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `attachment_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '附件名称',
  `attachment_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '附件类型：视频、文档、音频',
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',

  -- 标准字段
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_attachment_type` (`attachment_type`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='课程附件表';
```

## 表结构设计说明

### 设计思路：

1. **主课程表**：采用统一表结构，通过 `teach_type` 字段区分线上/线下课程，线下专用字段对线上课程为空值
2. **章节课时表**：仅用于线上课程，通过 `course_id` 字段关联主课程表
3. **附件表**：支持两种课程类型的附件管理
4. **索引设计**：为常用查询字段添加索引，提高查询性能

### 字段设计要点：

- 所有表都包含标准的审计字段（deleted、creator、create_time等）
- 使用合适的数据类型和长度
- 为枚举类型字段添加详细注释说明可选值
- 考虑了业务扩展性，预留了必要的字段空间
- 通过关联字段保证数据关联关系

### 支持的业务功能：

- ✅ 课程列表查询和筛选
- ✅ 课程新增和编辑
- ✅ 线上课程章节课时管理
- ✅ 课程附件管理
- ✅ 课程状态管理（上架/下架）
- ✅ 多租户数据隔离

### 常用查询示例：

#### 1. 查询课程列表（支持筛选）

```sql
SELECT
    id, name, teach_type, cover_url, category, status,
    teacher_id, teacher_name, business_module, merchant, enrolled_count,
    create_time, update_time
FROM publicbiz_digital_asset_course
WHERE deleted = 0
    AND tenant_id = 1
    AND (category = '家政技能' OR '家政技能' = '')
    AND (status = '已上架' OR '已上架' = '')
    AND (teach_type = '线上授课' OR '线上授课' = '')
    AND (business_module = '家政服务' OR '家政服务' = '')
    AND (merchant = '汇成人力资源' OR '汇成人力资源' = '')
    AND (name LIKE '%关键词%' OR '关键词' = '')
ORDER BY create_time DESC
LIMIT 0, 10;
```

#### 2. 查询线上课程完整信息（包含章节课时）

```sql
-- 查询课程基本信息
SELECT * FROM publicbiz_digital_asset_course
WHERE id = 1 AND deleted = 0 AND tenant_id = 1;

-- 查询课程章节
SELECT * FROM publicbiz_course_chapter
WHERE course_id = 1 AND deleted = 0 AND tenant_id = 1
ORDER BY sort_order ASC;

-- 查询课程课时
SELECT * FROM publicbiz_course_lesson
WHERE course_id = 1 AND deleted = 0 AND tenant_id = 1
ORDER BY chapter_id ASC, sort_order ASC;
```

#### 3. 查询课程附件

```sql
SELECT * FROM publicbiz_course_attachment
WHERE course_id = 1 AND deleted = 0 AND tenant_id = 1
ORDER BY create_time DESC;
```

### 数据完整性约束：

1. **关联关系**：通过 `course_id`、`chapter_id` 等字段维护表间关联关系
2. **应用层控制**：删除课程时需要在应用层处理相关的章节、课时、附件数据
3. **非空约束**：关键字段如课程名称、授课方式、分类等设为NOT NULL
4. **默认值**：为状态、计数等字段设置合理的默认值

### 扩展性考虑：

1. **预留字段空间**：varchar字段长度适当冗余，便于后续扩展
2. **索引优化**：为常用查询字段建立索引
3. **多租户支持**：所有表都包含tenant_id字段
4. **软删除**：使用deleted字段实现软删除，保留数据历史
