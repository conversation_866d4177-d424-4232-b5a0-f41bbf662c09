<!--
  页面名称：分类管理
  功能描述：支持业务模块筛选、分类层级管理、添加、编辑、删除分类
-->
<template>
  <div class="sort-mgt-container">
    <!-- 标题和返回按钮 -->
    <div class="sort-mgt-header">
      <el-icon class="sort-mgt-icon">
        <Operation />
      </el-icon>
      <span class="sort-mgt-title">分类管理</span>
      <el-button class="sort-mgt-back" type="default" @click="onBack" plain>
        <el-icon>
          <ArrowLeft />
        </el-icon>
        返回考题管理
      </el-button>
    </div>

    <!-- 业务模块选择 -->
    <el-card class="sort-mgt-card">
      <el-form :inline="true" class="sort-mgt-form" @submit.prevent="onSearch">
        <el-form-item label="选择业务模块：">
          <el-select
            v-model="searchForm.biz"
            placeholder="请选择业务模块"
            style="width: 180px"
            @change="onSearch"
            clearable
          >
            <el-option
              v-for="item in bizOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分类层级管理表格 -->
    <div class="sort-mgt-table-wrap table-responsive">
      <div class="sort-mgt-table-header">
        <span class="sort-mgt-table-title">分类层级管理</span>
        <el-button type="primary" @click="onAdd" class="sort-mgt-add-btn">
          <el-icon><Plus /></el-icon>
          添加分类
        </el-button>
      </div>

      <!-- 表格加载状态 -->
      <el-table
        :data="tableData"
        border
        style="min-width: 1200px; width: 100%"
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <el-table-column prop="level1" label="一级名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="level1Code" label="一级代码" min-width="100" show-overflow-tooltip />
        <el-table-column prop="level2" label="二级名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="level2Code" label="二级代码" min-width="100" show-overflow-tooltip />
        <el-table-column prop="level3" label="三级名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="level3Code" label="三级代码" min-width="100" show-overflow-tooltip />
        <el-table-column prop="certName" label="认定点名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="certCode" label="认定点代码" min-width="110" show-overflow-tooltip />
        <el-table-column prop="biz" label="业务模块" min-width="100" />
        <el-table-column prop="creator" label="创建人" min-width="80" />
        <el-table-column prop="createTime" label="创建时间" min-width="100" />
        <el-table-column label="操作" min-width="140" align="center" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="onEdit(scope.row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="danger" link @click="onDelete(scope.row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <el-empty v-if="!loading && tableData.length === 0" description="暂无数据" />
    </div>

    <!-- 添加/编辑分类弹窗 -->
    <AddSort v-model:visible="addSortVisible" :edit-data="editSortData" @success="onAddSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Operation, ArrowLeft, Plus, Edit, Delete } from '@element-plus/icons-vue'
import AddSort from './AddSort.vue'

/** 考题分类接口类型定义 */
interface QuestionCategoryVO {
  id?: number
  level1: string
  level1Code: string
  level2: string
  level2Code: string
  level3: string
  level3Code: string
  certName: string
  certCode: string
  biz: string
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
}

/** 组件事件定义 */
const emit = defineEmits(['back'])

/** 搜索表单数据 */
const searchForm = reactive({
  biz: ''
})

/** 业务模块选项 */
const bizOptions = ref<Array<{ label: string; value: string }>>([
  { label: '家政业务', value: '家政业务' },
  { label: '高校业务', value: '高校业务' },
  { label: '培训业务', value: '培训业务' },
  { label: '认证业务', value: '认证业务' }
])

/** Mock数据 - 分类列表 */
const mockCategoryData: QuestionCategoryVO[] = [
  {
    id: 1,
    level1: '职业技能等级认定',
    level1Code: 'ZY001',
    level2: '家政服务类',
    level2Code: 'JZ001',
    level3: '家政服务员',
    level3Code: 'JZFW001',
    certName: '家庭清洁技能',
    certCode: 'JTQJ001',
    biz: '家政业务',
    creator: '王五',
    createTime: '2024-01-17'
  },
  {
    id: 2,
    level1: '专项职业能力考核',
    level1Code: 'ZX001',
    level2: '家政服务类',
    level2Code: 'JZ002',
    level3: '育婴员',
    level3Code: 'YYY001',
    certName: '婴儿护理技能',
    certCode: 'YEHL001',
    biz: '家政业务',
    creator: '赵六',
    createTime: '2024-01-18'
  },
  {
    id: 3,
    level1: '职业技能等级认定',
    level1Code: 'ZY002',
    level2: '高校服务类',
    level2Code: 'GX001',
    level3: '教学管理员',
    level3Code: 'JXGL001',
    certName: '教学管理技能',
    certCode: 'JXGL002',
    biz: '高校业务',
    creator: '李四',
    createTime: '2024-01-19'
  },
  {
    id: 4,
    level1: '专项职业能力考核',
    level1Code: 'ZX002',
    level2: '培训服务类',
    level2Code: 'PX001',
    level3: '培训师',
    level3Code: 'PXS001',
    certName: '培训技能认定',
    certCode: 'PXJN001',
    biz: '培训业务',
    creator: '张三',
    createTime: '2024-01-20'
  }
]

/** 表格数据 */
const tableData = ref<QuestionCategoryVO[]>([])

/** 加载状态 */
const loading = ref(false)

/** 添加/编辑弹窗状态 */
const addSortVisible = ref(false)
const editSortData = ref<QuestionCategoryVO | null>(null)

/** 获取业务模块选项 */
const fetchBizOptions = async () => {
  // Mock异步操作
  await new Promise((resolve) => setTimeout(resolve, 100))
  // 业务模块选项已在初始化时设置，无需额外操作
}

/** 获取分类列表 */
const fetchCategoryList = async () => {
  try {
    loading.value = true

    // Mock异步操作
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 根据搜索条件过滤数据
    let filteredData = [...mockCategoryData]
    if (searchForm.biz) {
      filteredData = filteredData.filter((item) => item.biz === searchForm.biz)
    }

    tableData.value = filteredData
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
    tableData.value = []
  } finally {
    loading.value = false
  }
}

/** 搜索 */
const onSearch = () => {
  fetchCategoryList()
}

/** 重置搜索 */
const onReset = () => {
  searchForm.biz = ''
  fetchCategoryList()
}

/** 返回考题管理 */
const onBack = () => {
  emit('back')
}

/** 添加分类 */
const onAdd = () => {
  editSortData.value = null
  addSortVisible.value = true
}

/** 编辑分类 */
const onEdit = (row: QuestionCategoryVO) => {
  editSortData.value = { ...row }
  addSortVisible.value = true
}

/** 删除分类 */
const onDelete = async (row: QuestionCategoryVO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${row.level1} > ${row.level2} > ${row.level3}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // Mock异步删除操作
    await new Promise((resolve) => setTimeout(resolve, 300))

    // 从mock数据中删除
    const index = mockCategoryData.findIndex((item) => item.id === row.id)
    if (index > -1) {
      mockCategoryData.splice(index, 1)
    }

    ElMessage.success('删除成功')
    fetchCategoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

/** 根据业务模块生成代码前缀 */
const getBizPrefix = (biz: string): string => {
  switch (biz) {
    case '家政业务':
      return 'JZ'
    case '高校业务':
      return 'GX'
    case '培训业务':
      return 'PX'
    case '认证业务':
      return 'RZ'
    default:
      return 'ZY'
  }
}

/** 添加新分类到mock数据 */
const addCategoryToMock = (newCategory: QuestionCategoryVO) => {
  // 生成新的ID
  const maxId = Math.max(...mockCategoryData.map((item) => item.id || 0))
  newCategory.id = maxId + 1
  newCategory.createTime = new Date().toISOString().split('T')[0]
  newCategory.creator = '当前用户'

  // 智能生成代码字段
  const bizPrefix = getBizPrefix(newCategory.biz)

  if (!newCategory.level1Code || newCategory.level1Code === 'AUTO') {
    newCategory.level1Code = `${bizPrefix}${String(newCategory.id).padStart(3, '0')}`
  }
  if (!newCategory.level2Code || newCategory.level2Code === 'AUTO') {
    if (newCategory.level2) {
      newCategory.level2Code = `${bizPrefix}2${String(newCategory.id).padStart(2, '0')}`
    } else {
      newCategory.level2Code = ''
    }
  }
  if (!newCategory.level3Code || newCategory.level3Code === 'AUTO') {
    if (newCategory.level3) {
      newCategory.level3Code = `${bizPrefix}3${String(newCategory.id).padStart(2, '0')}`
    } else {
      newCategory.level3Code = ''
    }
  }
  if (!newCategory.certCode || newCategory.certCode === 'AUTO') {
    newCategory.certCode = `${bizPrefix}C${String(newCategory.id).padStart(2, '0')}`
  }

  // 清理空字段，确保数据一致性
  if (!newCategory.level2) {
    newCategory.level2Code = ''
  }
  if (!newCategory.level3) {
    newCategory.level3Code = ''
  }

  mockCategoryData.push(newCategory)
}

/** 更新mock数据中的分类 */
const updateCategoryInMock = (updatedCategory: QuestionCategoryVO) => {
  const index = mockCategoryData.findIndex((item) => item.id === updatedCategory.id)
  if (index > -1) {
    mockCategoryData[index] = { ...updatedCategory }
  }
}

/** 添加/编辑成功回调 */
const onAddSuccess = (categoryData?: QuestionCategoryVO, isEdit?: boolean) => {
  if (categoryData) {
    if (isEdit) {
      updateCategoryInMock(categoryData)
    } else {
      addCategoryToMock(categoryData)
    }
  }
  fetchCategoryList()
}

/** 组件挂载时初始化数据 */
onMounted(() => {
  fetchBizOptions()
  fetchCategoryList()
})
</script>

<style scoped lang="scss">
.sort-mgt-container {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 24px;
}

.sort-mgt-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.sort-mgt-icon {
  font-size: 20px;
  color: #409eff;
}

.sort-mgt-title {
  font-size: 20px;
  font-weight: bold;
  color: #1f2937;
}

.sort-mgt-back {
  margin-left: auto;
  font-size: 14px;
}

.sort-mgt-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: none;
}

.sort-mgt-form {
  padding: 8px 0 0 8px;
}

.sort-mgt-table-wrap {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(240, 241, 242, 0.8);
}

.sort-mgt-table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.sort-mgt-table-title {
  font-size: 16px;
  font-weight: bold;
  color: #1f2937;
}

.sort-mgt-add-btn {
  font-size: 14px;
}

.table-responsive {
  width: 100%;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sort-mgt-table-title {
    font-size: 14px;
  }

  :deep(.el-table th),
  :deep(.el-table td) {
    font-size: 13px;
  }
}

@media (max-width: 900px) {
  .sort-mgt-header {
    flex-wrap: wrap;
  }

  .sort-mgt-table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  :deep(.el-table th),
  :deep(.el-table td) {
    font-size: 12px;
  }

  .table-responsive {
    min-width: 800px;
  }
}
</style>
