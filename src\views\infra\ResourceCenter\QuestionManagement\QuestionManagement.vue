<!--
  页面名称：题库管理
  功能描述：展示题库列表，支持多级分类筛选、题型、业务模块、题干内容搜索，支持分页、操作下拉菜单
-->
<template>
  <div v-if="!showSortMgt">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg1">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon1"><QuestionFilled /></el-icon>
            <div>
              <div class="stat-num">{{ stat.total }}</div>
              <div class="stat-label">题目总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg2">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon2"><Tickets /></el-icon>
            <div>
              <div class="stat-num">{{ stat.single }}</div>
              <div class="stat-label">选择题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg3">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon3"><CircleCheck /></el-icon>
            <div>
              <div class="stat-num">{{ stat.judge }}</div>
              <div class="stat-label">判断题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card stat-bg4">
          <div class="stat-card-inner">
            <el-icon class="stat-icon stat-icon4"><Edit /></el-icon>
            <div>
              <div class="stat-num">{{ stat.short }}</div>
              <div class="stat-label">简答题</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <div class="search-fields">
        <el-form-item label="一级名称：">
          <el-select
            v-model="searchForm.level1"
            placeholder="全部一级"
            style="width: 120px"
            size="small"
          >
            <el-option label="全部一级" value="" />
            <el-option v-for="item in level1Options" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="二级名称：">
          <el-select
            v-model="searchForm.level2"
            placeholder="全部二级"
            style="width: 120px"
            size="small"
          >
            <el-option label="全部二级" value="" />
            <el-option v-for="item in level2Options" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="三级名称：">
          <el-select
            v-model="searchForm.level3"
            placeholder="全部三级"
            style="width: 120px"
            size="small"
          >
            <el-option label="全部三级" value="" />
            <el-option v-for="item in level3Options" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="题型：" class="type-select-item">
          <el-select
            v-model="searchForm.type"
            placeholder="全部题型"
            style="width: 100px"
            size="small"
          >
            <el-option label="全部题型" value="" />
            <el-option v-for="item in typeOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务模块：">
          <el-select v-model="searchForm.biz" placeholder="全部" style="width: 120px" size="small">
            <el-option label="全部" value="" />
            <el-option v-for="item in bizOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索题干内容..."
            style="width: 200px"
            clearable
            size="small"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch" size="small">筛选</el-button>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-user-solid" size="small" @click="onShowSortMgt"
            >分类管理</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-download" size="small" @click="downloadTemplate"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-upload"
            size="small"
            @click="batchImportVisible = true"
            >批量导入</el-button
          >
        </el-form-item>
      </div>
    </el-form>

    <!-- 表格区加横向滚动 -->
    <div class="table-responsive">
      <el-table :data="tableData" border style="min-width: 1100px; width: 100%">
        <el-table-column prop="level1" label="一级名称" min-width="100" />
        <el-table-column prop="level2" label="二级名称" min-width="100" />
        <el-table-column prop="level3" label="三级名称" min-width="100" />
        <el-table-column prop="certName" label="认定点名称" min-width="120" />
        <el-table-column prop="title" label="题干" min-width="200" />
        <el-table-column prop="type" label="题型" min-width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.type === '单选'" type="primary">单选</el-tag>
            <el-tag v-else-if="scope.row.type === '多选'" type="success">多选</el-tag>
            <el-tag v-else-if="scope.row.type === '判断'" type="warning">判断</el-tag>
            <el-tag v-else-if="scope.row.type === '简答'" type="danger">简答</el-tag>
            <el-tag v-else>{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="biz" label="业务模块" min-width="100" />
        <el-table-column prop="answer" label="参考答案" min-width="200" show-overflow-tooltip />
        <el-table-column prop="creator" label="创建人" min-width="80" />
        <el-table-column prop="createTime" label="创建时间" min-width="100" />
        <el-table-column label="操作" min-width="100" fixed="right">
          <template #default="scope">
            <el-dropdown @command="handleCommand(scope.row, $event)">
              <el-button size="small" type="primary">
                操作 <el-icon class="el-icon--right"><i class="el-icon-arrow-down"></i></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                  <el-dropdown-item command="log">操作日志</el-dropdown-item>
                  <el-dropdown-item command="delete" style="color: #ff4d4f">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页区 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      layout="total, prev, pager, next, jumper"
      @current-change="fetchList"
      style="margin-top: 16px; text-align: right"
    />
    <AddQuestion v-model:visible="editVisible" :form-data="editForm" />
    <!-- 在表格和分页后面添加抽屉 -->
    <el-drawer v-model="logVisible" direction="rtl" size="500px" :with-header="false">
      <QuestionOptLog @close="logVisible = false" />
    </el-drawer>
    <!-- 批量导入抽屉 -->
    <el-drawer v-model="batchImportVisible" direction="rtl" size="500px" :with-header="false">
      <BatchImportQuestion @close="batchImportVisible = false" @submit="onBatchImportSubmit" />
    </el-drawer>
  </div>
  <div v-else>
    <SortManagement @back="onBackSortMgt" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { QuestionFilled, Tickets, CircleCheck, Edit } from '@element-plus/icons-vue'
import AddQuestion from './components/AddQuestion.vue'
import QuestionOptLog from './components/QuestionOptLog.vue'
import SortManagement from './components/SortManagement.vue'
import { ElMessage } from 'element-plus'
import BatchImportQuestion from './components/BatchImportQuestion.vue'
const batchImportVisible = ref(false)
function onBatchImportSubmit(file: File) {
  // TODO: 批量导入逻辑
  batchImportVisible.value = false
}

/** 统计卡片数据 */
const stat = ref({ total: 15, single: 6, judge: 2, short: 2 })

/** 搜索表单数据 */
const searchForm = ref({
  level1: '',
  level2: '',
  level3: '',
  type: '',
  biz: '',
  keyword: ''
})
const level1Options = ['职业技能等级认定', '专项职业能力考核', '职业资格认证']
const level2Options = ['家政服务类', '技术工人类', '专业技术类', '管理人员类']
const level3Options = [
  '家政服务员',
  '育婴员',
  '电工',
  '健康管理师',
  '母婴护理',
  '焊工',
  '美容师',
  '车工'
]
const typeOptions = ['单选', '多选', '判断', '简答']
const bizOptions = ['家政业务', '高校业务', '培训业务', '认证业务']

/** 表格数据 */
const tableData = ref([
  {
    level1: '职业技能等级认定',
    level2: '家政服务类',
    level3: '家政服务员',
    certName: '职业道德基础',
    title: '家政服务员职业道德的核心要求是什么？',
    type: '单选',
    biz: '家政业务',
    answer:
      'A. 诚实守信、尊重客户 B. 只关注工作效率 C. 随意处理客户物品 D. 不需要保护客户隐私 正确答案：A',
    creator: '张老师',
    createTime: '2024-01-15'
  },
  {
    level1: '职业技能等级认定',
    level2: '家政服务类',
    level3: '育婴员',
    certName: '婴幼儿护理知识',
    title: '以下哪些是育婴员需要掌握的基本技能？',
    type: '多选',
    biz: '家政业务',
    answer: 'A. 婴幼儿喂养 B. 婴幼儿护理 C. 早期教育 D. 安全防护 正确答案：A、B、C、D',
    creator: '王老师',
    createTime: '2024-02-10'
  },
  {
    level1: '职业技能等级认定',
    level2: '技术工人类',
    level3: '电工',
    certName: '电工安全操作',
    title: '电工作业时必须穿戴绝缘手套和绝缘鞋。',
    type: '判断',
    biz: '培训业务',
    answer: '正确。电工作业时穿戴绝缘防护用品是基本安全要求。',
    creator: '李师傅',
    createTime: '2024-03-05'
  },
  {
    level1: '专项职业能力考核',
    level2: '专业技术类',
    level3: '健康管理师',
    certName: '健康评估方法',
    title: '请简述健康管理师在进行健康评估时的应遵循的基本原则。',
    type: '简答',
    biz: '认证业务',
    answer:
      '健康管理师在进行健康评估时应遵循的基本原则包括：1.全面性原则；2.科学性原则；3.个体化原则；4.动态性原则；5.保密性原则。',
    creator: '赵医生',
    createTime: '2024-03-20'
  },
  {
    level1: '职业技能等级认定',
    level2: '家政服务类',
    level3: '母婴护理',
    certName: '新生儿护理',
    title: '新生儿脐带护理的正确方法是？',
    type: '单选',
    biz: '家政业务',
    answer: 'A. 每天用酒精消毒 B. 保持干燥清洁 C. 用碘伏涂抹 D. 包扎严密 正确答案：B',
    creator: '陈护士',
    createTime: '2024-04-01'
  },
  {
    level1: '职业技能等级认定',
    level2: '家政服务类',
    level3: '母婴护理',
    certName: '新生儿护理',
    title: '母婴护理师需要掌握哪些技能？',
    type: '多选',
    biz: '家政业务',
    answer: 'A. 新生儿护理 B. 产妇护理 C. 营养配餐 D. 心理疏导 正确答案：A、B、C、D',
    creator: '孙师傅',
    createTime: '2024-04-15'
  },
  {
    level1: '职业技能等级认定',
    level2: '技术工人类',
    level3: '焊工',
    certName: '焊接安全规范',
    title: '焊接作业前需要检查哪些安全设备？',
    type: '判断',
    biz: '培训业务',
    answer: '正确。焊接作业前必须检查焊接设备、防护用品、通风设备等安全设施。',
    creator: '周老师',
    createTime: '2024-06-25'
  },
  {
    level1: '职业资格认证',
    level2: '管理人员类',
    level3: '美容师',
    certName: '皮肤护理基础',
    title: '不同肌肤类型需要使用不同的护肤产品和方法。',
    type: '简答',
    biz: '认证业务',
    answer:
      '不同肌肤类型的护理方法：1.干性肌肤：注重保湿，使用滋润型产品；2.油性肌肤：控油清洁，使用清爽型产品；3.混合性肌肤：分区护理；4.敏感性肌肤：温和护理，避免刺激性成分。',
    creator: '吴师傅',
    createTime: '2024-07-01'
  }
])

/** 分页信息 */
const pagination = ref({ page: 1, size: 10, total: 15 })

/** 获取题库列表（实际应调用接口，这里用mock数据） */
const fetchList = () => {
  // 实际开发中应根据searchForm和分页参数请求接口
}

const onSearch = () => {
  pagination.value.page = 1
  fetchList()
}

const editVisible = ref(false)
const editForm = ref({})

const onEdit = (row: any) => {
  editForm.value = { ...row }
  editVisible.value = true
}
const onDelete = (row: any) => {
  const idx = tableData.value.findIndex((item) => item === row)
  if (idx !== -1) {
    tableData.value.splice(idx, 1)
    ElMessage.success('删除成功')
  }
}
const onDetail = (row: any) => {
  // TODO: 详情逻辑
}

const onCopy = (row: any) => {
  editForm.value = { ...row }
  editVisible.value = true
}
const logVisible = ref(false)
const onLog = (row: any) => {
  logVisible.value = true
}

const showSortMgt = ref(false)

const onShowSortMgt = () => {
  showSortMgt.value = true
}
const onBackSortMgt = () => {
  showSortMgt.value = false
}

const downloadTemplate = () => {
  const link = document.createElement('a')
  link.href = '/templates/考题导入模板.csv'
  link.download = '考题导入模板.csv'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

function handleCommand(row: any, command: string) {
  if (command === 'edit') onEdit(row)
  else if (command === 'copy') onCopy(row)
  else if (command === 'log') onLog(row)
  else if (command === 'delete') onDelete(row)
}

onMounted(fetchList)
</script>

<style scoped lang="scss">
.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.stat-icon1 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67c23a;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #e6a23c;
  background: #fff5e6;
}
.stat-icon4 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.mb-4 {
  margin-bottom: 24px;
}
.mb-2 {
  margin-bottom: 12px;
}
.search-form {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-bottom: 12px;
  .el-form-item {
    margin-bottom: 0;
    margin-right: 4px;
  }
  .btn-group {
    display: flex;
    align-items: center;
    gap: 2px;
    margin-left: 4px;
    white-space: nowrap;
  }
}
.search-fields {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}
.search-btns {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  align-items: center;
  justify-content: flex-start;
}
.search-form-actions {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}
.table-responsive {
  width: 100%;
  overflow-x: auto;
}
@media (max-width: 1400px) {
  .table-responsive {
    min-width: 1100px;
  }
}
@media (max-width: 1200px) {
  .stat-card-inner {
    gap: 8px;
  }
  .stat-num {
    font-size: 15px;
  }
  .stat-label {
    font-size: 11px;
  }
  .el-table th,
  .el-table td {
    font-size: 13px;
  }
  .search-form {
    gap: 4px 0;
  }
  .btn-group .el-button {
    margin-bottom: 2px;
  }
  .search-form-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .search-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .search-fields {
    flex-wrap: wrap;
    gap: 4px;
  }
  .search-btns {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .search-btns .el-button {
    width: 100%;
  }
}
@media (max-width: 900px) {
  .stat-card-inner {
    gap: 4px;
  }
  .stat-num {
    font-size: 13px;
  }
  .stat-label {
    font-size: 10px;
  }
  .el-table th,
  .el-table td {
    font-size: 12px;
  }
  .search-form {
    flex-wrap: wrap;
    flex-direction: column;
    gap: 2px 0;
  }
  .table-responsive {
    min-width: 700px;
  }
  .btn-group {
    flex-wrap: wrap;
    flex-direction: column;
  }
  .btn-group .el-button {
    width: 100%;
    margin-bottom: 2px;
  }
  .search-fields {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
.search-row {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  justify-content: flex-start;
}
.search-row-actions {
  align-items: flex-start;
}
.btn-group {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: 0;
}
// 新增紧凑按钮组样式
.compact-btn-group {
  gap: 0;
}
.delete-btn {
  background-color: #ff4d4f !important;
  color: #fff !important;
  border: none !important;
}
@media (max-width: 1200px) {
  .search-row,
  .search-row-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  .btn-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    width: 100%;
  }
  .btn-group .el-button {
    width: 100%;
  }
}
.type-select-item {
  margin-left: -8px;
}
@media (max-width: 1200px) {
  .type-select-item {
    margin-left: 0;
  }
}
</style>
