<!--
  页面名称：添加分类
  功能描述：新增/编辑考题分类，支持表单校验、提交、重置、编辑回显
-->
<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑分类' : '添加分类'"
    width="500px"
    :close-on-click-modal="false"
    @close="onCancel"
    destroy-on-close
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="formRef"
      label-width="80px"
      v-loading="loading"
      element-loading-text="处理中..."
    >
      <el-form-item label="所属上级" prop="parentLevel">
        <el-select
          v-model="form.parentLevel"
          placeholder="搜索或选择上级分类..."
          style="width: 100%"
          clearable
          filterable
        >
          <el-option
            v-for="item in parentLevelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="分类名称" prop="categoryName" required>
        <el-input
          v-model="form.categoryName"
          placeholder="请输入分类名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="分类代码" prop="categoryCode" required>
        <el-input
          v-model="form.categoryCode"
          placeholder="请输入分类代码"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <!-- 动态显示的认定点字段 -->
      <transition name="fade-slide">
        <div v-if="showCertFields" class="cert-fields">
          <el-form-item label="认定点名称" prop="certName">
            <el-input
              v-model="form.certName"
              placeholder="请输入认定点名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="认定点代码" prop="certCode">
            <el-input
              v-model="form.certCode"
              placeholder="请输入认定点代码"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
        </div>
      </transition>

      <el-form-item label="业务模块" prop="biz" required>
        <el-select v-model="form.biz" placeholder="高校业务" style="width: 100%" disabled>
          <el-option
            v-for="item in bizOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 添加说明 -->
    <div class="add-tips">
      <el-icon class="tips-icon"><InfoFilled /></el-icon>
      <span class="tips-title">添加说明</span>
      <ul class="tips-list">
        <li>不选择上级：添加一级分类</li>
        <li>选择一级分类：添加二级分类</li>
        <li>选择二级分类：添加三级分类（显示认定点名称）</li>
        <li>分类名称和代码为必填项</li>
      </ul>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="loading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'

/** 考题分类接口类型定义 */
interface QuestionCategoryVO {
  id?: number
  level1: string
  level1Code: string
  level2: string
  level2Code: string
  level3: string
  level3Code: string
  certName: string
  certCode: string
  biz: string
  creator?: string
  createTime?: string
  updater?: string
  updateTime?: string
}

/** 组件属性定义 */
interface Props {
  editData?: QuestionCategoryVO | null
}

/** 组件事件定义 */
const emit = defineEmits(['success'])

/** 组件属性 */
const props = withDefaults(defineProps<Props>(), {
  editData: null
})

/** 弹窗显示状态 */
const visible = defineModel<boolean>('visible', { default: false })

/** 表单引用 */
const formRef = ref()

/** 加载状态 */
const loading = ref(false)

/** 是否编辑模式 */
const isEdit = computed(() => !!props.editData?.id)

/** 是否显示认定点字段（当选择二级分类时显示） */
const showCertFields = computed(() => {
  return form.parentLevel && form.parentLevel.includes('level2')
})

/** 表单数据 */
const form = reactive({
  parentLevel: '',
  categoryName: '',
  categoryCode: '',
  certName: '',
  certCode: '',
  biz: '高校业务'
})

/** 上级分类选项 */
const parentLevelOptions = ref<Array<{ label: string; value: string }>>([
  { label: '装潢职业能力考核（ZX001）（一级）', value: 'zx001_level1' },
  { label: '职业技能等级认定（ZY001）（一级）', value: 'zy001_level1' },
  { label: '专项职业能力考核（ZX002）（一级）', value: 'zx002_level1' },
  { label: '家政服务类（JZ001）（二级）', value: 'jz001_level2' },
  { label: '高校服务类（GX001）（二级）', value: 'gx001_level2' }
])

/** 表单校验规则 */
const rules = reactive({
  parentLevel: [{ required: false, message: '请选择上级分类', trigger: 'change' }],
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  categoryCode: [
    { required: true, message: '请输入分类代码', trigger: 'blur' },
    { min: 2, max: 20, message: '分类代码长度在 2 到 20 个字符', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]+$/, message: '分类代码只能包含字母和数字', trigger: 'blur' }
  ],
  certName: [
    { required: false, message: '请输入认定点名称', trigger: 'blur' },
    { max: 50, message: '认定点名称长度不能超过 50 个字符', trigger: 'blur' }
  ],
  certCode: [
    { required: false, message: '请输入认定点代码', trigger: 'blur' },
    { max: 20, message: '认定点代码长度不能超过 20 个字符', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]*$/, message: '认定点代码只能包含字母和数字', trigger: 'blur' }
  ],
  biz: [{ required: true, message: '请选择业务模块', trigger: 'change' }]
})

/** 业务模块选项 */
const bizOptions = ref<Array<{ label: string; value: string }>>([
  { label: '家政业务', value: '家政业务' },
  { label: '高校业务', value: '高校业务' },
  { label: '培训业务', value: '培训业务' },
  { label: '认证业务', value: '认证业务' }
])

/** 获取业务模块选项 */
const fetchBizOptions = async () => {
  // Mock异步操作
  await new Promise((resolve) => setTimeout(resolve, 100))
  // 业务模块选项已在初始化时设置，无需额外操作
}

/** 重置表单 */
const resetForm = () => {
  form.parentLevel = ''
  form.categoryName = ''
  form.categoryCode = ''
  form.certName = ''
  form.certCode = ''
  form.biz = '高校业务' // 默认值
  formRef.value?.clearValidate()
}

/** 编辑时回显数据 */
const fillFormData = () => {
  if (props.editData) {
    // 根据数据层级确定上级分类和当前分类
    if (props.editData.level3) {
      // 三级分类，尝试找到匹配的二级分类作为上级
      const matchingParent = parentLevelOptions.value.find(
        (opt) => opt.label.includes(props.editData!.level2) && opt.value.includes('level2')
      )
      form.parentLevel = matchingParent?.value || 'gx001_level2'
      form.categoryName = props.editData.level3
      form.categoryCode = props.editData.level3Code || ''
    } else if (props.editData.level2) {
      // 二级分类，尝试找到匹配的一级分类作为上级
      const matchingParent = parentLevelOptions.value.find(
        (opt) => opt.label.includes(props.editData!.level1) && opt.value.includes('level1')
      )
      form.parentLevel = matchingParent?.value || 'zy001_level1'
      form.categoryName = props.editData.level2
      form.categoryCode = props.editData.level2Code || ''
    } else {
      // 一级分类，无上级
      form.parentLevel = ''
      form.categoryName = props.editData.level1
      form.categoryCode = props.editData.level1Code || ''
    }

    form.certName = props.editData.certName || ''
    form.certCode = props.editData.certCode || ''
    form.biz = props.editData.biz || '高校业务'
  }
}

/** 提交表单 */
const onSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    // Mock异步操作
    await new Promise((resolve) => setTimeout(resolve, 800))

    // 根据上级分类选择确定当前添加的层级
    let submitData: QuestionCategoryVO

    if (!form.parentLevel) {
      // 没有选择上级分类，添加一级分类
      submitData = {
        level1: form.categoryName,
        level1Code: form.categoryCode || 'AUTO',
        level2: '',
        level2Code: '',
        level3: '',
        level3Code: '',
        certName: form.certName || form.categoryName,
        certCode: form.certCode || 'AUTO',
        biz: form.biz
      }
    } else if (form.parentLevel.includes('level1')) {
      // 选择了一级分类，添加二级分类
      const parentOption = parentLevelOptions.value.find((opt) => opt.value === form.parentLevel)
      // 从上级分类选项中提取代码信息
      const parentCode = form.parentLevel.split('_')[0].toUpperCase()
      submitData = {
        level1: parentOption?.label || '上级分类',
        level1Code: parentCode || 'AUTO',
        level2: form.categoryName,
        level2Code: form.categoryCode || 'AUTO',
        level3: '',
        level3Code: '',
        certName: form.certName || form.categoryName,
        certCode: form.certCode || 'AUTO',
        biz: form.biz
      }
    } else if (form.parentLevel.includes('level2')) {
      // 选择了二级分类，添加三级分类
      const parentOption = parentLevelOptions.value.find((opt) => opt.value === form.parentLevel)
      // 从上级分类选项中提取代码信息
      const parentCode = form.parentLevel.split('_')[0].toUpperCase()
      submitData = {
        level1: '职业技能等级认定', // 从上级分类中提取
        level1Code: 'ZY001', // 默认一级代码
        level2: parentOption?.label.split('（')[0] || '上级二级分类', // 提取分类名称
        level2Code: parentCode || 'AUTO',
        level3: form.categoryName,
        level3Code: form.categoryCode || 'AUTO',
        certName: form.certName || form.categoryName,
        certCode: form.certCode || 'AUTO',
        biz: form.biz
      }
    } else {
      // 默认情况，添加一级分类
      submitData = {
        level1: form.categoryName,
        level1Code: form.categoryCode || 'AUTO',
        level2: '',
        level2Code: '',
        level3: '',
        level3Code: '',
        certName: form.certName || form.categoryName,
        certCode: form.certCode || 'AUTO',
        biz: form.biz
      }
    }

    if (isEdit.value) {
      submitData.id = props.editData!.id
      ElMessage.success('编辑成功')
      // 通知父组件更新数据
      emit('success', submitData, true)
    } else {
      ElMessage.success('添加成功')
      // 通知父组件添加数据
      emit('success', submitData, false)
    }

    visible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

/** 取消操作 */
const onCancel = () => {
  visible.value = false
}

/** 监听弹窗显示状态 */
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
    fillFormData()
  }
})

/** 监听上级分类变化，当不是二级分类时清空认定点字段 */
watch(
  () => form.parentLevel,
  (newVal) => {
    if (!newVal || !newVal.includes('level2')) {
      form.certName = ''
      form.certCode = ''
    }
  }
)

/** 组件挂载时初始化 */
onMounted(() => {
  fetchBizOptions()
})
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 18px;
}

.form-item-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;

  .el-icon {
    font-size: 14px;
    color: #409eff;
  }
}

.add-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;

  .tips-icon {
    color: #409eff;
    margin-right: 6px;
  }

  .tips-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
    display: inline-block;
  }

  .tips-list {
    margin: 8px 0 0 20px;
    padding: 0;

    li {
      margin-bottom: 4px;
      font-size: 13px;
      color: #606266;
      list-style-type: disc;
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

/* 认定点字段容器 */
.cert-fields {
  margin: 0;
  padding: 0;
}

/* 动态字段过渡效果 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
  overflow: hidden;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 600px) {
  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto;
  }

  .add-tips {
    font-size: 12px;

    .tips-list li {
      font-size: 12px;
    }
  }
}
</style>
