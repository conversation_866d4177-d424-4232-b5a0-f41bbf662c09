import request from '@/config/axios'

/**
 * 阿姨管理API接口
 */

// 阿姨信息接口
export interface Practitioner {
  id: string
  name: string
  phone: string
  idCard: string
  age: number
  gender: 'male' | 'female'
  avatar: string
  serviceTypes: string[]
  experience: number
  rating: number
  status: 'active' | 'inactive' | 'pending'
  platformStatus: 'online' | 'offline' | 'busy'
  agencyId: string
  agencyName: string
  createTime: string
  updateTime: string
}

// 查询参数接口
export interface PractitionerQueryParams {
  keyword?: string
  serviceType?: string
  platformStatus?: string
  rating?: number
  agencyId?: string
  pageNum: number
  pageSize: number
}

// 新增阿姨参数接口
export interface CreatePractitionerParams {
  name: string
  phone: string
  idCard: string
  age: number
  gender: 'male' | 'female'
  avatar?: string
  serviceTypes: string[]
  experience: number
  agencyId: string
}

// 更新阿姨参数接口
export interface UpdatePractitionerParams {
  id: string
  name?: string
  phone?: string
  age?: number
  gender?: 'male' | 'female'
  avatar?: string
  serviceTypes?: string[]
  experience?: number
  status?: 'active' | 'inactive' | 'pending'
}

/**
 * 获取阿姨列表
 * @param params 查询参数
 * @returns Promise<{ list: Practitioner[], total: number }>
 */
export function getPractitionerList(params: PractitionerQueryParams) {
  return request.get({
    url: '/mall/employment/practitioner/list',
    params
  })
}

/**
 * 获取阿姨详情
 * @param id 阿姨ID
 * @returns Promise<Practitioner>
 */
export function getPractitionerDetail(id: string) {
  return request.get({
    url: `/mall/employment/practitioner/${id}`
  })
}

/**
 * 新增阿姨
 * @param data 阿姨信息
 * @returns Promise<void>
 */
export function createPractitioner(data: CreatePractitionerParams) {
  return request.post({
    url: '/mall/employment/practitioner',
    data
  })
}

/**
 * 更新阿姨信息
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updatePractitioner(data: UpdatePractitionerParams) {
  return request.put({
    url: `/mall/employment/practitioner/${data.id}`,
    data
  })
}

/**
 * 删除阿姨
 * @param id 阿姨ID
 * @returns Promise<void>
 */
export function deletePractitioner(id: string) {
  return request.delete({
    url: `/mall/employment/practitioner/${id}`
  })
}

/**
 * 更新阿姨状态
 * @param id 阿姨ID
 * @param status 状态
 * @returns Promise<void>
 */
export function updatePractitionerStatus(id: string, status: 'active' | 'inactive' | 'pending') {
  return request.put({
    url: `/mall/employment/practitioner/${id}/status`,
    data: { status }
  })
}

/**
 * 生成阿姨简历
 * @param id 阿姨ID
 * @returns Promise<{ url: string }>
 */
export function generatePractitionerResume(id: string) {
  return request.post({
    url: `/mall/employment/practitioner/${id}/resume`
  })
}

/**
 * 终止阿姨合同
 * @param id 阿姨ID
 * @param reason 终止原因
 * @returns Promise<void>
 */
export function terminatePractitionerContract(id: string, reason: string) {
  return request.post({
    url: `/mall/employment/practitioner/${id}/terminate`,
    data: { reason }
  })
}

/**
 * 导出阿姨列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportPractitionerList(
  params: Omit<PractitionerQueryParams, 'pageNum' | 'pageSize'>
) {
  return request.download({
    url: '/mall/employment/practitioner/export',
    params
  })
}
